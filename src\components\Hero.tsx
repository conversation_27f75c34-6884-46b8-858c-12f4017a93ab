import { motion } from "framer-motion";
import {
  BsArrowRight,
  BsClipboardCheck,
  BsClipboardCheckFill,
  BsClock,
} from "react-icons/bs";
import { FaBusinessTime } from "react-icons/fa";

const Hero = () => {
  return (
    <section
      id="hero"
      className="relative flex min-h-screen items-center justify-center overflow-hidden pt-28 md:pt-20"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900 via-gray-900 to-gray-800"></div>

      <div className="container-custom relative z-10">
        <div className="flex flex-col items-center text-center">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="mb-6 text-4xl font-bold md:text-5xl lg:text-6xl"
          >
            We Build <br className="hidden md:block" />
            <span className="gradient-text">Content Automation</span> Ecosystems{" "}
            <br className="hidden md:block" /> for Creative Agencies
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-8 max-w-2xl text-lg text-gray-300"
          >
            Streamline your agency's workflow, scale content production, and
            increase ROI with our custom automation solutions designed for
            end-to-end social media and digital marketing agencies.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"
          >
            <a
              href="https://calendly.com/diftra/intro-call"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-primary flex items-center justify-center gap-2"
            >
              <span>Schedule a Free Strategy Call</span>
              <motion.span
                className="inline-block"
                animate={{ y: [0, -4, 0] }}
                transition={{
                  duration: 1,
                  repeat: Number.POSITIVE_INFINITY,
                  repeatDelay: 3.5,
                }}
              >
                <BsClipboardCheck />
              </motion.span>
            </a>
            <a href="#services" className="btn-secondary">
              Discover Our Solutions
            </a>
          </motion.div>
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 grid grid-cols-1 gap-24 rounded-lg border border-gray-800 bg-gray-800/30 p-8 backdrop-blur-sm sm:grid-cols-2 lg:grid-cols-3"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">
              <div className="inline-flex items-center justify-center">
                <span className="text-[1.55rem] mr-0.5 relative -top-1">9</span>
                <div className="h-8 w-0.5 bg-purple-400 transform rotate-[30deg] mx-0.5"></div>
                <span className="text-2xl ml-0.5 relative top-1">10</span>
              </div>
            </div>
            <div className="mt-1 text-gray-400">
              Time Saved on Content Creation
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">4x</div>
            <div className="mt-1 text-gray-400">
              Content Production Capacity
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">100%</div>
            <div className="mt-1 text-gray-400">Satisfaction Guarantee</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
