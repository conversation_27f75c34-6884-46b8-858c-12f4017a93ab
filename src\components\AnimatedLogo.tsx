import { animate, svg, utils } from "animejs";
import type React from "react";
import { useCallback, useEffect, useRef, memo } from "react";

// Define the types for props
export type AnimatedLogoTrigger = "mount" | "hover" | "scroll" | "both";
interface AnimatedLogoProps {
  svg: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  trigger?: AnimatedLogoTrigger;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeColor?: string;
}

// The component is memoized to prevent unnecessary re-renders
const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  svg: SVGComponent,
  trigger = "mount",
  duration = 2000,
  delay = 0,
  className,
  style,
  strokeColor,
}) => {
  // Refs for DOM elements and state management
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  // Internal ref to track animation state, preventing concurrent animations
  const animationRunning = useRef(false);
  // Ref to track the last animation start time for safety checks
  const lastAnimationStart = useRef<number>(0);

  // Helper function to set paths to their final drawn state
  const initializePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Set all paths to their fully drawn state instantly
      nodeList.forEach((el) => {
        const d = svg.createDrawable(el);
        animate(d, {
          draw: "1 1", // 100% drawn, no gap
          duration: 0,
          loop: false,
        });
      });
    } catch (e) {
      console.error("Failed to initialize logo paths:", e);
    }
  }, [strokeColor]);

  // Animation state management callbacks
  const animatePathsStarted = useCallback(() => {
    animationRunning.current = true;
    lastAnimationStart.current = Date.now();
  }, []);

  const animatePathsCompleted = useCallback(() => {
    animationRunning.current = false;
  }, []);

  // Core animation logic
  const animatePaths = useCallback(() => {
    // Prevent starting a new animation if one is already running
    if (animationRunning.current) return;

    // Prevent starting animation if tab is not visible
    if (document.visibilityState === "hidden") return;

    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      animatePathsStarted();

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Animation sequence timings
      const drawDuration = duration * 0.3;
      const pauseDuration = duration * 0.1;
      const undrawDuration = duration * 0.3;
      const staggerDelay = 100;

      const totalDrawTime = drawDuration + (nodeList.length - 1) * staggerDelay;
      const lastUndrawStartTime =
        delay + totalDrawTime + pauseDuration + (nodeList.length - 1) * staggerDelay;
      const totalAnimationDuration = lastUndrawStartTime + undrawDuration;

      // Animate each path
      for (let idx = 0; idx < nodeList.length; idx++) {
        const el = nodeList[idx];
        const d = svg.createDrawable(el);

        // Phase 1: Draw (from nothing to fully drawn)
        animate(d, {
          draw: ["0 0", "0 1"],
          easing: "easeOutQuad",
          duration: drawDuration,
          delay: delay + idx * staggerDelay,
          loop: false,
        });

        // Phase 2: Undraw (from fully drawn to gone)
        const undrawStartTime =
          delay + totalDrawTime + pauseDuration + idx * staggerDelay;
        setTimeout(() => {
          if (d) {
            animate(d, {
              draw: ["0 1", "1 1"],
              easing: "easeInQuad",
              duration: undrawDuration,
              loop: false,
            });
          }
        }, undrawStartTime);
      }

      // Set a single timeout to mark the animation as complete
      setTimeout(() => {
        animatePathsCompleted();
        // Ensure logo is in fully drawn state after animation completes
        setTimeout(() => {
          initializePaths();
        }, 100);
      }, totalAnimationDuration);
    } catch (e) {
      console.error("Failed to animate logo paths:", e);
      // Ensure the lock is released on error
      animatePathsCompleted();
    }
  }, [
    duration,
    delay,
    strokeColor,
    animatePathsStarted,
    animatePathsCompleted,
  ]);

  // Mount trigger
  useEffect(() => {
    if (trigger === "mount" || trigger === "both") {
      animatePaths();
    } else {
      // For hover/scroll, initialize paths to their final state
      initializePaths();
    }
  }, [animatePaths, initializePaths, trigger]);

  // Hover trigger
  useEffect(() => {
    if (trigger === "hover" || trigger === "both") {
      const handleHover = () => {
        // The check for animationRunning is now inside animatePaths
        animatePaths();
      };

      const node = containerRef.current;
      if (node) {
        node.addEventListener("mouseenter", handleHover);
        return () => node.removeEventListener("mouseenter", handleHover);
      }
    }
  }, [animatePaths, trigger]);

  // Scroll trigger (using Intersection Observer)
  useEffect(() => {
    if (trigger === "scroll" || trigger === "both") {
      const node = containerRef.current;
      if (node) {
        observerRef.current?.disconnect();
        observerRef.current = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              animatePaths();
            }
          },
          { threshold: 0.5 },
        );
        observerRef.current.observe(node);
        return () => observerRef.current?.disconnect();
      }
    }
  }, [animatePaths, trigger]);

  // Proactive logo state management to prevent glitches
  useEffect(() => {
    const resetLogoState = () => {
      try {
        // Forcefully stop any ongoing or pending animations from anime.js
        if (containerRef.current) {
          const svgEl = containerRef.current.querySelector("svg");
          if (svgEl) {
            const nodeList = svgEl.querySelectorAll("path, line, polyline");
            utils.remove(nodeList);
          }
        }

        // Force the animation lock to be released
        animationRunning.current = false;
        lastAnimationStart.current = 0;

        // Reset the logo to its fully drawn, non-animated state
        initializePaths();
      } catch (e) {
        console.error("Failed to reset logo state:", e);
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        // PROACTIVE: Reset immediately when tab loses focus
        resetLogoState();
      } else if (document.visibilityState === "visible") {
        // REACTIVE: Also reset when tab regains focus (backup)
        setTimeout(() => {
          resetLogoState();
        }, 50);
      }
    };

    const handleBlur = () => {
      // PROACTIVE: Reset when window loses focus
      resetLogoState();
    };

    const handleFocus = () => {
      // REACTIVE: Reset when window regains focus (backup)
      setTimeout(() => {
        resetLogoState();
      }, 50);
    };

    const handlePageHide = () => {
      // PROACTIVE: Reset when page is being hidden
      resetLogoState();
    };

    const handlePageShow = (event: PageTransitionEvent) => {
      // REACTIVE: Handle browser back/forward navigation
      if (event.persisted) {
        setTimeout(() => {
          resetLogoState();
        }, 50);
      }
    };

    // Add event listeners for both proactive and reactive handling
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("blur", handleBlur);
    window.addEventListener("focus", handleFocus);
    window.addEventListener("pagehide", handlePageHide);
    window.addEventListener("pageshow", handlePageShow);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("blur", handleBlur);
      window.removeEventListener("focus", handleFocus);
      window.removeEventListener("pagehide", handlePageHide);
      window.removeEventListener("pageshow", handlePageShow);
    };
  }, [initializePaths]);

  // Lightweight safety check mechanism (backup only)
  useEffect(() => {
    const safetyCheck = () => {
      // Only check if tab is visible and animation is supposedly running
      if (document.visibilityState === "visible" && animationRunning.current) {
        const now = Date.now();
        const maxAnimationDuration = duration + delay + 3000; // Reduced buffer since we're more proactive

        // If animation has been running for too long, reset it
        if (
          lastAnimationStart.current > 0 &&
          (now - lastAnimationStart.current) > maxAnimationDuration
        ) {
          console.warn("Logo animation stuck, resetting...");

          // Force cleanup and reset
          if (containerRef.current) {
            const svgEl = containerRef.current.querySelector("svg");
            if (svgEl) {
              const nodeList = svgEl.querySelectorAll("path, line, polyline");
              utils.remove(nodeList);
            }
          }

          animationRunning.current = false;
          lastAnimationStart.current = 0;
          initializePaths();
        }
      }
    };

    // Run safety check less frequently since we're being proactive
    const interval = setInterval(safetyCheck, 10000);

    return () => clearInterval(interval);
  }, [duration, delay, initializePaths]);

  if (!SVGComponent) return null;

  return (
    <div ref={containerRef} className={className} style={style}>
      <SVGComponent
        style={{ display: "block", width: "100%", height: "100%" }}
      />
    </div>
  );
};

export default memo(AnimatedLogo);
