import { animate, svg, utils } from "animejs";
import type React from "react";
import { useCallback, useEffect, useRef, memo } from "react";

// Define the types for props
export type AnimatedLogoTrigger = "mount" | "hover" | "scroll" | "both";
interface AnimatedLogoProps {
  svg: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  trigger?: AnimatedLogoTrigger;
  duration?: number;
  delay?: number;
  className?: string;
  style?: React.CSSProperties;
  strokeColor?: string;
}

// The component is memoized to prevent unnecessary re-renders
const AnimatedLogo: React.FC<AnimatedLogoProps> = ({
  svg: SVGComponent,
  trigger = "mount",
  duration = 2000,
  delay = 0,
  className,
  style,
  strokeColor,
}) => {
  // Refs for DOM elements and state management
  const containerRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  // Internal ref to track animation state, preventing concurrent animations
  const animationRunning = useRef(false);
  // Ref to track the last animation start time for safety checks
  const lastAnimationStart = useRef<number>(0);
  // Refs to track all pending timeouts and animations for cleanup
  const pendingTimeouts = useRef<Set<number>>(new Set());
  const activeAnimations = useRef<Set<any>>(new Set());

  // Comprehensive cleanup function to stop all animations and timeouts
  const cleanupAllAnimations = useCallback(() => {
    try {
      // Clear all pending timeouts
      pendingTimeouts.current.forEach((timeoutId) => {
        clearTimeout(timeoutId);
      });
      pendingTimeouts.current.clear();

      // Aggressively stop ALL anime.js animations globally
      try {
        // Remove all animations from our specific elements
        if (containerRef.current) {
          const svgEl = containerRef.current.querySelector("svg");
          if (svgEl) {
            const nodeList = svgEl.querySelectorAll("path, line, polyline");
            utils.remove(nodeList);

            // Force reset all path attributes to prevent partial states
            nodeList.forEach((el) => {
              const pathEl = el as SVGPathElement;
              if (pathEl.getTotalLength) {
                const length = pathEl.getTotalLength();
                pathEl.style.strokeDasharray = `${length}`;
                pathEl.style.strokeDashoffset = '0';
              }
            });
          }
        }
      } catch (e) {
        console.warn("Failed to reset SVG paths:", e);
      }

      // Clear active animations tracking
      activeAnimations.current.clear();

      // Reset animation state
      animationRunning.current = false;
      lastAnimationStart.current = 0;
    } catch (e) {
      console.error("Failed to cleanup animations:", e);
    }
  }, []);

  // Helper function to set paths to their final drawn state
  const initializePaths = useCallback(() => {
    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Set all paths to their fully drawn state instantly
      nodeList.forEach((el) => {
        const d = svg.createDrawable(el);
        animate(d, {
          draw: "1 1", // 100% drawn, no gap
          duration: 0,
          loop: false,
        });
      });
    } catch (e) {
      console.error("Failed to initialize logo paths:", e);
    }
  }, [strokeColor]);

  // Animation state management callbacks
  const animatePathsStarted = useCallback(() => {
    animationRunning.current = true;
    lastAnimationStart.current = Date.now();
  }, []);

  const animatePathsCompleted = useCallback(() => {
    animationRunning.current = false;
  }, []);

  // Core animation logic
  const animatePaths = useCallback(() => {
    // Prevent starting a new animation if one is already running
    if (animationRunning.current) return;

    // Prevent starting animation if tab is not visible
    if (document.visibilityState === "hidden") return;

    // Immediately cleanup any existing animations before starting new ones
    cleanupAllAnimations();

    try {
      if (!containerRef.current) return;
      const svgEl = containerRef.current.querySelector("svg");
      if (!svgEl) return;
      const nodeList = svgEl.querySelectorAll("path, line, polyline");
      if (!nodeList.length) return;

      animatePathsStarted();

      // Apply stroke color if provided
      if (strokeColor) {
        for (const el of nodeList) {
          (el as SVGElement).setAttribute("stroke", strokeColor);
        }
      }

      // Animation sequence timings
      const drawDuration = duration * 0.3;
      const pauseDuration = duration * 0.1;
      const undrawDuration = duration * 0.3;
      const staggerDelay = 100;

      const totalDrawTime = drawDuration + (nodeList.length - 1) * staggerDelay;
      const lastUndrawStartTime =
        delay + totalDrawTime + pauseDuration + (nodeList.length - 1) * staggerDelay;
      const totalAnimationDuration = lastUndrawStartTime + undrawDuration;

      // Animate each path
      for (let idx = 0; idx < nodeList.length; idx++) {
        const el = nodeList[idx];
        const d = svg.createDrawable(el);

        // Track this animation
        activeAnimations.current.add(d);

        // Phase 1: Draw (from nothing to fully drawn)
        animate(d, {
          draw: ["0 0", "0 1"],
          easing: "easeOutQuad",
          duration: drawDuration,
          delay: delay + idx * staggerDelay,
          loop: false,
        });

        // Phase 2: Undraw (from fully drawn to gone)
        const undrawStartTime =
          delay + totalDrawTime + pauseDuration + idx * staggerDelay;
        const undrawTimeoutId = window.setTimeout(() => {
          // Check if animation is still valid (not cleaned up)
          if (d && animationRunning.current && document.visibilityState === "visible") {
            animate(d, {
              draw: ["0 1", "1 1"],
              easing: "easeInQuad",
              duration: undrawDuration,
              loop: false,
            });
          }
          // Remove this timeout from tracking
          pendingTimeouts.current.delete(undrawTimeoutId);
        }, undrawStartTime);

        // Track this timeout
        pendingTimeouts.current.add(undrawTimeoutId);
      }

      // Set a single timeout to mark the animation as complete
      const completionTimeoutId = window.setTimeout(() => {
        // Only complete if animation is still valid
        if (animationRunning.current && document.visibilityState === "visible") {
          animatePathsCompleted();
          // Ensure logo is in fully drawn state after animation completes
          const finalizeTimeoutId = window.setTimeout(() => {
            if (document.visibilityState === "visible") {
              initializePaths();
            }
            pendingTimeouts.current.delete(finalizeTimeoutId);
          }, 100);
          pendingTimeouts.current.add(finalizeTimeoutId);
        }
        // Remove this timeout from tracking
        pendingTimeouts.current.delete(completionTimeoutId);
      }, totalAnimationDuration);

      // Track this timeout
      pendingTimeouts.current.add(completionTimeoutId);
    } catch (e) {
      console.error("Failed to animate logo paths:", e);
      // Ensure the lock is released on error
      animatePathsCompleted();
    }
  }, [
    duration,
    delay,
    strokeColor,
    animatePathsStarted,
    animatePathsCompleted,
  ]);

  // Mount trigger
  useEffect(() => {
    if (trigger === "mount" || trigger === "both") {
      animatePaths();
    } else {
      // For hover/scroll, initialize paths to their final state
      initializePaths();
    }
  }, [animatePaths, initializePaths, trigger]);

  // Hover trigger
  useEffect(() => {
    if (trigger === "hover" || trigger === "both") {
      const handleHover = () => {
        // The check for animationRunning is now inside animatePaths
        animatePaths();
      };

      const node = containerRef.current;
      if (node) {
        node.addEventListener("mouseenter", handleHover);
        return () => node.removeEventListener("mouseenter", handleHover);
      }
    }
  }, [animatePaths, trigger]);

  // Scroll trigger (using Intersection Observer)
  useEffect(() => {
    if (trigger === "scroll" || trigger === "both") {
      const node = containerRef.current;
      if (node) {
        observerRef.current?.disconnect();
        observerRef.current = new IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              animatePaths();
            }
          },
          { threshold: 0.5 },
        );
        observerRef.current.observe(node);
        return () => observerRef.current?.disconnect();
      }
    }
  }, [animatePaths, trigger]);

  // Proactive logo state management to prevent glitches
  useEffect(() => {
    const resetLogoState = () => {
      try {
        // Use comprehensive cleanup function
        cleanupAllAnimations();

        // Reset the logo to its fully drawn, non-animated state
        initializePaths();
      } catch (e) {
        console.error("Failed to reset logo state:", e);
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === "hidden") {
        // PROACTIVE: Reset immediately when tab loses focus
        resetLogoState();
      } else if (document.visibilityState === "visible") {
        // REACTIVE: Force reset when tab regains focus
        resetLogoState();
        // Additional reset after a short delay as backup
        setTimeout(() => {
          resetLogoState();
        }, 100);
      }
    };

    const handleBlur = () => {
      // PROACTIVE: Reset when window loses focus
      resetLogoState();
    };

    const handleFocus = () => {
      // REACTIVE: Force reset when window regains focus
      resetLogoState();
    };

    const handlePageHide = () => {
      // PROACTIVE: Reset when page is being hidden
      resetLogoState();
    };

    const handlePageShow = (event: PageTransitionEvent) => {
      // REACTIVE: Handle browser back/forward navigation
      if (event.persisted) {
        setTimeout(() => {
          resetLogoState();
        }, 50);
      }
    };

    // Add event listeners for both proactive and reactive handling
    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("blur", handleBlur);
    window.addEventListener("focus", handleFocus);
    window.addEventListener("pagehide", handlePageHide);
    window.addEventListener("pageshow", handlePageShow);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("blur", handleBlur);
      window.removeEventListener("focus", handleFocus);
      window.removeEventListener("pagehide", handlePageHide);
      window.removeEventListener("pageshow", handlePageShow);
    };
  }, [initializePaths, cleanupAllAnimations]);

  // Aggressive safety check mechanism
  useEffect(() => {
    const safetyCheck = () => {
      // Always reset if tab is visible but animation state seems stuck
      if (document.visibilityState === "visible") {
        const now = Date.now();
        const maxAnimationDuration = duration + delay + 2000;

        // If animation has been running for too long, or if we detect any inconsistency
        if (
          (animationRunning.current && lastAnimationStart.current > 0 &&
           (now - lastAnimationStart.current) > maxAnimationDuration) ||
          (animationRunning.current && pendingTimeouts.current.size === 0)
        ) {
          console.warn("Logo animation stuck or inconsistent, resetting...");
          cleanupAllAnimations();
          initializePaths();
        }
      }
    };

    // Run safety check more frequently to catch issues quickly
    const interval = setInterval(safetyCheck, 2000);

    return () => clearInterval(interval);
  }, [duration, delay, initializePaths, cleanupAllAnimations]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupAllAnimations();
    };
  }, [cleanupAllAnimations]);

  if (!SVGComponent) return null;

  return (
    <div ref={containerRef} className={className} style={style}>
      <SVGComponent
        style={{ display: "block", width: "100%", height: "100%" }}
      />
    </div>
  );
};

export default memo(AnimatedLogo);
