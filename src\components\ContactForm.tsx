import { motion } from "framer-motion";
import { useState } from "react";
import { BsCameraVideoFill } from "react-icons/bs";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Check, FiSend } from "react-icons/fi";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError("");

    // Check if we're in development mode (using window location)
    const isDevelopment =
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1";

    try {
      let result: { success: boolean; message?: string };

      if (isDevelopment) {
        // In development, simulate a successful response
        console.log("Development mode: Simulating form submission", formData);

        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        result = { success: true };
      } else {
        // In production, send actual request to PHP script
        const response = await fetch("/send-email.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        });

        result = await response.json();
      }

      if (result.success) {
        setIsSubmitted(true);

        // Reset form after successful submission
        setFormData({
          name: "",
          email: "",
          company: "",
          message: "",
        });

        // Reset submission status after some time
        setTimeout(() => {
          setIsSubmitted(false);
        }, 5000);
      } else {
        setError(result.message || "Failed to send message. Please try again.");
      }
    } catch (err) {
      setError("An error occurred. Please try again later.");
      console.error("Form submission error:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="section-padding bg-gray-900">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <h2 className="section-title">
            Get In <span className="gradient-text">Touch</span>
          </h2>
          <p className="section-subtitle">
            Have questions or want to learn more? Reach out to us!
          </p>
        </motion.div>

        <div className="mx-auto mt-12 grid max-w-6xl gap-12 md:grid-cols-2 grid-cols-1 md:order-1">
          {/* Contact Form - First on mobile */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="order-1 md:order-2"
          >
            <div className="rounded-lg border border-gray-800 bg-gray-800/30 p-6">
              {isSubmitted ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <div className="mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-600/20 text-purple-400">
                    <FiCheck size={32} />
                  </div>
                  <h3 className="mb-2 text-2xl font-bold">Message Sent!</h3>
                  <p className="text-gray-400">
                    We'll get back to you as soon as possible.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label
                      htmlFor="name"
                      className="mb-2 block text-sm font-medium"
                    >
                      Your Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full rounded-md border border-gray-700 bg-gray-800 px-4 py-3 text-white focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                      placeholder="John Doe"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="email"
                      className="mb-2 block text-sm font-medium"
                    >
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full rounded-md border border-gray-700 bg-gray-800 px-4 py-3 text-white focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="company"
                      className="mb-2 block text-sm font-medium"
                    >
                      Agency / Company
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full rounded-md border border-gray-700 bg-gray-800 px-4 py-3 text-white focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                      placeholder="Your Agency"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="message"
                      className="mb-2 block text-sm font-medium"
                    >
                      Your Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={4}
                      className="w-full rounded-md border border-gray-700 bg-gray-800 px-4 py-3 text-white focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                      placeholder="Tell us about your needs..."
                    ></textarea>
                  </div>

                  {error && <p className="text-sm text-red-500">{error}</p>}

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="btn-primary flex w-full items-center justify-center gap-2"
                  >
                    {isSubmitting ? (
                      <span>Sending...</span>
                    ) : (
                      <>
                        <span>Send Message</span>
                        <FiSend className="send-icon" />
                      </>
                    )}
                  </button>
                </form>
              )}
            </div>
          </motion.div>

          {/* Contact Information - Second on mobile */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-col justify-center order-2 md:order-1"
          >
            <div className="space-y-4 mb-8">
              <div>
                <h4 className="mb-2 text-xl font-semibold">Our Commitment</h4>
                <p className="text-gray-400">
                  We're dedicated to helping agencies scale their content
                  production through innovative automation solutions.
                </p>
              </div>
              <div>
                <h4 className="mb-2 text-xl font-semibold">Response Time</h4>
                <p className="text-gray-400">
                  We respond to all inquiries within 24 hours during business
                  days.
                </p>
              </div>
            </div>

            <div className="rounded-lg border border-gray-800 bg-gray-800/30 p-6">
              <h3 className="mb-4 text-2xl font-bold">
                Prefer a Direct Conversation?
              </h3>
              <p className="mb-6 text-gray-400">
                Schedule a free 30-minute strategy call with our team to discuss
                your agency's specific needs.
              </p>
              <a
                href="https://calendly.com/diftra/intro-call"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary inline-flex items-center gap-2"
              >
                <span>Let's Chat</span>
                <motion.span
                  className="inline-block text-white"
                  animate={{ rotate: [-10, 10, -10, 0] }}
                  transition={{
                    duration: 0.8,
                    repeat: Number.POSITIVE_INFINITY,
                    repeatDelay: 3,
                    times: [0, 0.25, 0.5, 0.75],
                  }}
                >
                  <BsCameraVideoFill className="text-white" />
                </motion.span>
              </a>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
